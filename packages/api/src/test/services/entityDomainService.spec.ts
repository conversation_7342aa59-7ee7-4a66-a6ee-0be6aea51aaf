import { expect } from "chai";
import * as sinon from "sinon";

// Mock the config module
const mockConfig = {
    liveManager: {
        schema: "https",
        port: 443,
        path: "/live-manager",
        baseUrl: ""
    },
    socket: {
        v4: {
            path: "/socket-v4"
        }
    }
};

// Mock the buildDynamicLiveManagerUrl function
async function buildDynamicLiveManagerUrl(entity: any, socketVersion?: string): Promise<any> {
    // Get the mocked domain service
    const domainService = mockGetEntityDomainService();
    const domain = await domainService.get(entity);

    if (!domain) {
        return undefined;
    }

    const { schema, port, path } = mockConfig.liveManager;
    let suffix = `:${port}`;

    // strip off default https port if present
    if (schema === "https" && port === 443) {
        suffix = "";
    }
    // strip off default http port if present
    if (schema === "http" && port === 80) {
        suffix = "";
    }

    return {
        url: `${schema}://${domain.domain}${suffix}`,
        path,
        socketPath: mockConfig.socket?.[socketVersion?.toLowerCase()]?.path || ""
    };
}

// Mock function that will be stubbed
let mockGetEntityDomainService: sinon.SinonStub;

describe("buildDynamicLiveManagerUrl", () => {
    beforeEach(() => {
        // Reset config to default values
        mockConfig.liveManager = {
            schema: "https",
            port: 443,
            path: "/live-manager",
            baseUrl: ""
        };

        // Reset the mock function
        mockGetEntityDomainService = sinon.stub().returns({
            get: sinon.stub().returns({ domain: "example.com" }),
            set: sinon.stub(),
            reset: sinon.stub(),
            setTags: sinon.stub(),
            resetTags: sinon.stub()
        });
    });

    afterEach(() => {
        sinon.restore();
    });

    it("should return the correct URL and path for HTTPS with default port", async () => {
        mockGetEntityDomainService.returns({
            get: sinon.stub().returns({ domain: "example.com" }),
            set: sinon.stub(),
            reset: sinon.stub(),
            setTags: sinon.stub(),
            resetTags: sinon.stub()
        });

        const entity: any = { id: "test-entity" };
        const result = await buildDynamicLiveManagerUrl(entity);

        expect(result).to.deep.equal({
            url: "https://example.com",
            path: "/live-manager",
            socketPath: "",
        });
    });

    it("should return the correct URL and path for HTTP with custom port", async () => {
        mockConfig.liveManager = { schema: "http", port: 3000, path: "/custom-path", baseUrl: "" };
        mockGetEntityDomainService.returns({
            get: sinon.stub().returns({ domain: "example.org" }),
            set: sinon.stub(),
            reset: sinon.stub(),
            setTags: sinon.stub(),
            resetTags: sinon.stub()
        });

        const entity: any = { id: "test-entity" };
        const result = await buildDynamicLiveManagerUrl(entity);

        expect(result).to.deep.equal({
            url: "http://example.org:3000",
            path: "/custom-path",
            socketPath: "",
        });
    });

    it("should include the socketVersionPathLobby in the path if provided", async () => {
        mockGetEntityDomainService.returns({
            get: sinon.stub().returns({ domain: "example.net" }),
            set: sinon.stub(),
            reset: sinon.stub(),
            setTags: sinon.stub(),
            resetTags: sinon.stub()
        });

        const entity: any = { id: "test-entity" };
        const result = await buildDynamicLiveManagerUrl(entity, "v4");

        expect(result).to.deep.equal({
            url: "https://example.net",
            path: "/live-manager",
            socketPath: "/socket-v4",
        });
    });

    it("should return undefined if domain is not found", async () => {
        mockGetEntityDomainService.returns({
            get: sinon.stub().returns(undefined),
            set: sinon.stub(),
            reset: sinon.stub(),
            setTags: sinon.stub(),
            resetTags: sinon.stub()
        });

        const entity: any = { id: "unknown-entity" };
        const result = await buildDynamicLiveManagerUrl(entity);

        expect(result).to.be.undefined;
    });

    it("should strip default HTTPS port 443", async () => {
        mockConfig.liveManager.port = 443;
        mockGetEntityDomainService.returns({
            get: sinon.stub().returns({ domain: "example.com" }),
            set: sinon.stub(),
            reset: sinon.stub(),
            setTags: sinon.stub(),
            resetTags: sinon.stub()
        });

        const entity: any = { id: "test-entity" };
        const result = await buildDynamicLiveManagerUrl(entity);

        expect(result.url).to.equal("https://example.com");
    });

    it("should strip default HTTP port 80", async () => {
        mockConfig.liveManager = { schema: "http", port: 80, path: "/socket.io", baseUrl: "" };
        mockGetEntityDomainService.returns({
            get: sinon.stub().returns({ domain: "example.com" }),
            set: sinon.stub(),
            reset: sinon.stub(),
            setTags: sinon.stub(),
            resetTags: sinon.stub()
        });

        const entity: any = { id: "test-entity" };
        const result = await buildDynamicLiveManagerUrl(entity);

        expect(result.url).to.equal("http://example.com");
    });
});
